const { describeWithTransaction } = require('../../utils/describeWithTransaction');
const GenerateJobDescService = require('../../../src/services/job_vacancy/GenerateJobDescService');

describeWithTransaction('GenerateJobDescService', () => {
  let service;
  let mockOnetService;
  let mockGoogleAiService;

  beforeEach(() => {
    mockOnetService = {
      getOccupations: jest.fn(),
      getKnowledges: jest.fn(),
      getSkills: jest.fn(),
      getAbilities: jest.fn(),
      getTasks: jest.fn(),
    };

    mockGoogleAiService = {
      generateContent: jest.fn(),
      embedContent: jest.fn(),
    };

    service = new GenerateJobDescService({
      onetService: mockOnetService,
      googleAiService: mockGoogleAiService,
    });

    // Mock qdrantClient
    service.qdrantClient = {
      searchBatch: jest.fn(),
      retrieve: jest.fn(),
    };
  });

  describe('constructor', () => {
    it('should initialize with onetService and googleAiService', () => {
      expect(service.onetService).toBe(mockOnetService);
      expect(service.googleAiService).toBe(mockGoogleAiService);
    });
  });

  describe('generateJobDesc', () => {
    it('should generate job description successfully', async () => {
      const mockJobTitle = 'Software Engineer';
      const mockTopUserIds = [1, 2, 3];

      jest
        .spyOn(service, 'getRelatedJobTitles')
        .mockResolvedValue(['Software Engineer', 'Developer']);
      jest.spyOn(service, 'generateVectorTitles').mockResolvedValue(['Software Engineer']);
      jest.spyOn(service, 'qdrantSearchOnet').mockResolvedValue([]);
      jest.spyOn(service, 'qdrantSearchInternal').mockResolvedValue([]);
      jest.spyOn(service, 'processContextData').mockResolvedValue([]);
      jest.spyOn(service, 'generateJobDescriptionWithAI').mockResolvedValue({
        title: 'Software Engineer',
        summary: 'Generated job description',
      });

      const result = await service.generateJobDesc(mockJobTitle, mockTopUserIds);

      expect(result).toHaveProperty('jobDescription');
      expect(result).toHaveProperty('jobTitle', mockJobTitle);
      expect(result).toHaveProperty('onetsocCodes');
    });
  });

  describe('getRelatedJobTitles', () => {
    it('should return related job titles', async () => {
      const jobTitle = 'Software Engineer';
      mockGoogleAiService.generateContent.mockResolvedValue({
        candidates: [
          {
            content: {
              parts: [
                {
                  text: JSON.stringify([
                    'Senior Software Engineer',
                    'Full Stack Developer',
                    'Backend Developer',
                  ]),
                },
              ],
            },
          },
        ],
      });
      const result = await service.getRelatedJobTitles(jobTitle);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(3);
      expect(result).toEqual([
        'Senior Software Engineer',
        'Full Stack Developer',
        'Backend Developer',
      ]);
    });
  });

  describe('generateVectorTitles', () => {
    it('should generate vector titles from job titles', async () => {
      const jobTitles = ['Software Engineer', 'Developer'];

      mockGoogleAiService.generateContent.mockResolvedValue({
        candidates: [
          {
            content: {
              parts: [
                {
                  text: JSON.stringify(['Software Engineer', 'Developer']),
                },
              ],
            },
          },
        ],
      });

      mockGoogleAiService.embedContent.mockResolvedValue({
        embeddings: [{ values: [0.1, 0.2, 0.3] }, { values: [0.4, 0.5, 0.6] }],
      });

      const result = await service.generateVectorTitles(jobTitles);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(2);
      expect(result[0]).toHaveProperty('jobTitle', 'Software Engineer');
      expect(result[0]).toHaveProperty('vector', [0.1, 0.2, 0.3]);
      expect(result[1]).toHaveProperty('jobTitle', 'Developer');
      expect(result[1]).toHaveProperty('vector', [0.4, 0.5, 0.6]);
    });
  });

  describe('qdrantSearchOnet', () => {
    it('should search ONET data in Qdrant', async () => {
      const vectorizedJobTitles = [{ jobTitle: 'Software Engineer', vector: [0.1, 0.2, 0.3] }];

      service.qdrantClient.searchBatch.mockResolvedValue([
        [
          {
            id: 'some-id-1',
            payload: { onetsoc_code: '15-1132.00', job_title: 'Software Engineer' },
          },
        ],
      ]);

      service.qdrantClient.retrieve.mockResolvedValue([
        { payload: { onetsoc_code: '15-1132.00', job_title: 'Software Engineer' } },
      ]);

      const result = await service.qdrantSearchOnet(vectorizedJobTitles);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('onetsoc_code', '15-1132.00');
      expect(result[0]).toHaveProperty('title', 'Software Engineer');
      expect(service.qdrantClient.searchBatch).toHaveBeenCalled();
      expect(service.qdrantClient.retrieve).toHaveBeenCalled();
    });
  });

  describe('qdrantSearchInternal', () => {
    it('should search internal data in Qdrant', async () => {
      const vectorizedJobTitles = [{ jobTitle: 'Software Engineer', vector: [0.1, 0.2, 0.3] }];

      service.qdrantClient.searchBatch.mockResolvedValue([
        [{ id: 'some-id-2', payload: { table_internal_id: 1, job_title: 'Software Engineer' } }],
      ]);

      service.qdrantClient.retrieve.mockResolvedValue([
        { payload: { table_internal_id: 1, job_title: 'Software Engineer' } },
      ]);

      const result = await service.qdrantSearchInternal(vectorizedJobTitles);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('table_internal_id', 1);
      expect(result[0]).toHaveProperty('title', 'Software Engineer');
      expect(service.qdrantClient.searchBatch).toHaveBeenCalled();
      expect(service.qdrantClient.retrieve).toHaveBeenCalled();
    });
  });

  describe('onetContextData', () => {
    it('should process ONET search results correctly', async () => {
      const mockOnetResults = [{ onetsoc_code: '15-1132.00' }];

      mockOnetService.getOccupations.mockResolvedValue([
        { title: 'Software Developer', description: 'Develops software applications' },
      ]);
      mockOnetService.getTasks.mockResolvedValue([
        { task: 'Write code', task_type: 'Core', importance: 'High' },
      ]);
      mockOnetService.getKnowledges.mockResolvedValue([
        { name: 'Computer Programming', level: 4.5 },
      ]);
      mockOnetService.getSkills.mockResolvedValue([{ name: 'Programming', level: 4.0 }]);
      mockOnetService.getAbilities.mockResolvedValue([{ name: 'Deductive Reasoning', level: 3.5 }]);

      const result = await service.onetContextData(mockOnetResults);

      expect(result).toHaveProperty('occupations');
      expect(result).toHaveProperty('tasks');
      expect(result).toHaveProperty('source');
      expect(result.source).toBe('onet');
      expect(mockOnetService.getOccupations).toHaveBeenCalledWith(['15-1132.00']);
    });

    it('should handle empty search results', async () => {
      mockOnetService.getOccupations.mockResolvedValue([]);
      mockOnetService.getTasks.mockResolvedValue([]);

      const result = await service.onetContextData([]);

      expect(result).toEqual({
        onetData: 'No onet data available',
        source: 'onet',
      });
    });
  });

  describe('internalContextData', () => {
    it('should process internal search results correctly', async () => {
      const mockSearchResults = [
        {
          job_title: 'Software Engineer',
          requirements: ["Bachelor's degree"],
          responsibilities: ['Develop software'],
        },
      ];

      const result = await service.internalContextData(mockSearchResults);

      expect(result).toHaveProperty('internalData');
      expect(result).toHaveProperty('source');
      expect(result.source).toBe('internal');
      expect(typeof result.internalData).toBe('string');
    });

    it('should handle empty search results', async () => {
      const result = await service.internalContextData([]);

      expect(result).toEqual({
        internalData: 'No internal data available',
        source: 'internal',
      });
    });
  });

  describe('processContextData', () => {
    it('should process both ONET and internal data', async () => {
      jest.spyOn(service, 'qdrantSearchOnet').mockResolvedValue([]);
      jest.spyOn(service, 'qdrantSearchInternal').mockResolvedValue([]);
      jest.spyOn(service, 'onetContextData').mockResolvedValue({
        occupations: '',
        tasks: '',
        source: 'onet',
      });
      jest.spyOn(service, 'internalContextData').mockResolvedValue({
        internalData: '',
        source: 'internal',
      });

      const searchResults = {
        onetResults: [{ onetsoc_code: '15-1132.00' }],
        internalResults: [{ table_internal_id: 1 }],
      };

      jest.spyOn(service, 'onetContextData').mockResolvedValue({
        occupations: 'Mock onet occupations',
        tasks: 'Mock onet tasks',
        source: 'onet',
      });
      jest.spyOn(service, 'internalContextData').mockResolvedValue({
        internalData: 'Mock internal data',
        source: 'internal',
      });
      jest.spyOn(service, 'userAssessmentData').mockResolvedValue({
        userAssessmentData: 'Mock user assessment data',
        source: 'user_assessment',
      });

      const result = await service.processContextData(searchResults, [1, 2]);

      expect(result).toBeInstanceOf(Object);
      expect(result).toHaveProperty('onetContext');
      expect(result).toHaveProperty('internalContext');
      expect(result).toHaveProperty('userAssessmentContext');
      expect(service.onetContextData).toHaveBeenCalledWith(searchResults.onetResults);
      expect(service.internalContextData).toHaveBeenCalledWith(searchResults.internalResults);
      expect(service.userAssessmentData).toHaveBeenCalledWith([1, 2]);
    });
  });

  describe('generateJobDescriptionWithAI', () => {
    it('should generate job description using AI', async () => {
      const jobTitle = 'Software Engineer';
      const contextData = [
        {
          jobTitle: 'Software Engineer',
          onetContext: { occupations: '', tasks: '', source: 'onet' },
          internalContext: { internalData: '', source: 'internal' },
          userAssessmentContext: { userAssessmentData: 'Mock data', source: 'user_assessment' },
        },
      ];

      mockGoogleAiService.generateContent.mockResolvedValue({
        candidates: [
          {
            content: {
              parts: [
                {
                  text: JSON.stringify({
                    title: 'Software Engineer',
                    summary: 'Generated job description content',
                  }),
                },
              ],
            },
          },
        ],
      });

      const result = await service.generateJobDescriptionWithAI(jobTitle, contextData);

      expect(result).toHaveProperty('title', 'Software Engineer');
      expect(result).toHaveProperty('summary', 'Generated job description content');
      expect(mockGoogleAiService.generateContent).toHaveBeenCalled();
    });
  });

  describe('getSystemPrompt', () => {
    it('should construct system prompt with job title and context data', async () => {
      const jobTitle = 'Software Engineer';
      const contextData = {
        internalContext: { internalData: 'Mock internal data', source: 'internal' },
        onetContext: { occupations: 'Mock occupations', tasks: 'Mock tasks', source: 'onet' },
        tasks: 'Mock tasks',
        userAssessmentContext: { userAssessmentData: 'Mock assessment', source: 'user_assessment' },
      };

      const result = await service.getSystemPrompt(jobTitle, contextData);

      expect(typeof result).toBe('string');
      expect(result).toContain('TARGET_JOB_TITLE');
      expect(result).toContain('JSON');
    });
  });

  describe('getUserPrompt', () => {
    it('should construct user prompt with job title and context data', async () => {
      const jobTitle = 'Software Engineer';
      const contextData = [
        {
          jobTitle: 'Software Engineer',
          onetContext: { occupations: '', tasks: '', source: 'onet' },
          internalContext: { internalData: '', source: 'internal' },
          userAssessmentContext: { userAssessmentData: 'Mock data', source: 'user_assessment' },
        },
      ];

      const result = await service.getUserPrompt(jobTitle, contextData);

      expect(typeof result).toBe('string');
      expect(result).toContain(jobTitle);
    });
  });

  describe('userAssessmentData', () => {
    it('should return user assessment data', async () => {
      const userIds = [1, 2, 3];
      const result = await service.userAssessmentData(userIds);

      expect(result).toHaveProperty('userAssessmentData');
      expect(result).toHaveProperty('source');
      expect(result.source).toBe('user_assessment');
    });

    it('should handle empty user IDs', async () => {
      const result = await service.userAssessmentData([]);

      expect(result).toHaveProperty('userAssessmentData');
      expect(result).toHaveProperty('source');
      expect(result.source).toBe('user_assessment');
      expect(result.userAssessmentData).toBe('No user assessment data available');
    });
  });
});
