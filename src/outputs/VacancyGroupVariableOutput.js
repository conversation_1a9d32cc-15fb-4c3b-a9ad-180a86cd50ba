const ApiOutput = require('./ApiOutput');

class VacancyGroupVariableOutput extends ApiOutput {
  /**
   * Format a single vacancy group variable for output
   * @returns {Object} Formatted vacancy group variable data
   */
  format() {
    const vgv = this.data;
    const jgv = this.options.jgvsById[vgv.job_group_variable_id];

    return {
      id: vgv.id,
      job_group_variable: {
        id: jgv.id,
        name: jgv.name,
        description: jgv.description,
      },
      keyword_match_count: vgv.keyword_match_count,
      keyword_total_count: vgv.keyword_total_count,
      match_type: vgv.match_type,
      weight: vgv.weight,
      filters: vgv.filters,
      order_level: jgv.order_level,
    };
  }
}

module.exports = VacancyGroupVariableOutput;
