const ApiOutput = require('./ApiOutput');

class UserJobVacancyOutput extends ApiOutput {
  /**
   * Format the UserJobVacancy data for the API response.
   * @param {Object} item - A UserJobVacancy model instance with nested user and profile data (for array formatting)
   * @returns {Object} Formatted data.
   */
  format() {
    const record = this.data;
    const user = record.user || {};

    return {
      id: record.id,
      user: {
        id: user.id,
        name: user.name,
      },
      status: record.status,
      match_rate: record.match_rate,
      variable_groups: this.variableGroupsOutput(),
    };
  }

  variableGroupsOutput() {
    const groupScoresById = this.options.groupScoresById || {};
    return groupScoresById[this.data.id] || [];
  }
}

module.exports = UserJobVacancyOutput;
