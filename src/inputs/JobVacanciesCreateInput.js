const ApplicationInput = require('./ApplicationInput');

class JobVacanciesCreateInput extends ApplicationInput {
  /**
   * Get the JSON schema for validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
        },
        job_title_id: {
          type: 'integer',
        },
        department: {
          type: 'string',
        },
        job_grade: {
          type: 'string',
        },
        job_description: {
          type: 'string',
        },
        reference_user_ids: {
          type: 'array',
          items: {
            type: 'integer',
          },
          default: [],
        },
        detailed_descriptions: {
          type: 'object',
          properties: {
            key_responsibilities: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            qualifications: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            competencies: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            success_metrics: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
          },
        },
        job_level_id: {
          type: 'integer',
        },
      },
      required: [],
      additionalProperties: false,
    };
  }

  output() {
    const data = super.output();

    data.related_user_ids = data.reference_user_ids;
    delete data.reference_user_ids;

    return data;
  }
}

module.exports = JobVacanciesCreateInput;
