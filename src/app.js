const express = require('express');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');

const config = require('./config/config');
const { sequelize } = require('./config/sequelize');

// Import middleware
const { rateLimiters } = require('./middlewares/rateLimiter');
const errorHandler = require('./middlewares/errorHandler');
const {
  prometheusMiddleware,
  metricsHandler,
  setupSequelizeMetrics,
} = require('./middlewares/prometheus');

// Setup automatic database metrics tracking
if (process.env.NODE_ENV !== 'test' || config.enableSequelizeMetrics) {
  setupSequelizeMetrics(sequelize);
}

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const userPositionsRoutes = require('./routes/userPositions');
const jobTitleRoutes = require('./routes/jobTitles');
const jobLevelRoutes = require('./routes/jobLevels');
const jobVacancyRoutes = require('./routes/jobVacancies');
const userJobVacancyRoutes = require('./routes/userJobVacancies');
const vacancyGroupVariableRoutes = require('./routes/vacancyGroupVariables');

const app = express();

// Express middlewares
app.use(compression());
app.use(helmet());
app.disable('x-powered-by');

// Prometheus metrics middleware (before rate limiting)
app.use(prometheusMiddleware);

// Rate limiting middleware (apply to all routes)
app.use(rateLimiters.general);

// CORS configuration
app.use(
  cors({
    origin: config.corsOrigin || '*',
    credentials: true,
  }),
);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: config.nodeEnv || 'development',
  });
});

// Prometheus metrics endpoint
app.get('/metrics', metricsHandler);

// API routes with specific rate limiting
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/users', userRoutes);
app.use('/api/v1/user_positions', rateLimiters.general, userPositionsRoutes);
app.use('/api/v1/job_titles', rateLimiters.general, jobTitleRoutes);
app.use('/api/v1/job_levels', rateLimiters.general, jobLevelRoutes);
app.use('/api/v1/job_vacancies', rateLimiters.general, jobVacancyRoutes);
app.use('/api/v1/user_job_vacancies', rateLimiters.general, userJobVacancyRoutes);
app.use('/api/v1/vacancy_group_variables', rateLimiters.general, vacancyGroupVariableRoutes);

// Error handler middleware
app.use(errorHandler);

module.exports = app;
